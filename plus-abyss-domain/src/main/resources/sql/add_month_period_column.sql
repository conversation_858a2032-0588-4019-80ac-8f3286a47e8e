-- 为订单表增加会员月卡期数字段
-- 执行时间：2025-08-19
-- 说明：为 plus_order_info 表添加 month_period 字段，用于记录会员月卡的期数信息

-- 添加字段
ALTER TABLE plus_order_info ADD COLUMN month_period INT DEFAULT NULL COMMENT '会员月卡第几期（1表示第1期，2表示第2期，以此类推）';

-- 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'plus_order_info' AND COLUMN_NAME = 'month_period';

-- 查看表结构
-- DESC plus_order_info;
