# 会员月卡期数字段实现总结

## 概述
本次实现为订单表增加了会员月卡期数字段（month_period），并完善了相关业务逻辑，包括主动下单期数设置和定时任务创建续费订单功能。

## 1. 数据库层修改

### 1.1 表结构变更
- **文件**: `plus-abyss-domain/src/main/resources/sql/add_month_period_column.sql`
- **变更**: 为 `plus_order_info` 表添加 `month_period` 字段
- **SQL**: `ALTER TABLE plus_order_info ADD COLUMN month_period INT DEFAULT NULL COMMENT '会员月卡第几期（1表示第1期，2表示第2期，以此类推）';`

### 1.2 PO 类修改
- **文件**: `PlusOrderInfoPo.java`
- **变更**: 添加 `monthPeriod` 字段及 getter/setter 方法

### 1.3 Mapper XML 修改
- **文件**: `PlusOrderInfoMapper.xml`
- **变更**:
  - 更新 `Base_Column_List` 包含 `month_period` 字段
  - 更新 `PlusOrderInfo` 和 `PlusOrderEntity` ResultMap 映射
  - 修改 `insertPlusOrderInfo`、`updatePlusOrderInfo`、`updatePlusOrderInfoByOrderSn` 语句

## 2. 实体类修改

### 2.1 领域实体
- **文件**: `PlusOrderEntity.java`
- **变更**: 添加 `monthPeriod` 字段及 getter/setter 方法

### 2.2 事件类
- **文件**: `PlusOrderCreateEvent.java`
- **变更**: 添加 `monthPeriod` 字段

### 2.3 应用对象
- **文件**: `PlusOrderAo.java`
- **变更**: 添加 `monthPeriod` 字段

## 3. 业务逻辑修改

### 3.1 主动下单逻辑
- **文件**: `PlusOrderApplicationImpl.java`
- **方法**: `createPlusOrder`
- **变更**: 
  - 添加 `setMonthPeriod` 私有方法
  - 在订单创建时设置期数逻辑：
    - 会员月卡首单：设置期数为 1
    - 续费订单：从续费计划中获取期数

### 3.2 续费订单期数设置
- **文件**: `PlusOrderApplicationImpl.java`
- **方法**: `createPlusOrderByRenewPlan`
- **变更**: 在续费订单创建时从续费计划中获取 `currentPeriod` 设置期数

## 4. 转换器修改

### 4.1 模型层转换器
- **文件**: `IPlusOrderModelConverter.java`
- **变更**: 在 `toOrderEntity` 方法映射中添加 `monthPeriod` 字段映射

### 4.2 应用层转换器
- **文件**: `IPlusOrderApplicationConverter.java`
- **变更**: `toPlusOrderAo` 方法自动映射 `monthPeriod` 字段（同名字段自动映射）

## 5. 新增功能实现

### 5.1 会员月卡续费应用服务
- **接口**: `IPlusMonthMemberRenewalApplication.java`
- **实现**: `PlusMonthMemberRenewalApplicationImpl.java`
- **功能**:
  - 定时任务创建续费订单
  - 单个续费订单创建
  - 批量续费订单创建

### 5.2 定时任务
- **文件**: `PlusMonthMemberRenewalSchedule.java`
- **功能**: 每天上午9点执行会员月卡续费订单创建任务
- **Cron**: `0 0 9 * * ?`

### 5.3 枚举类
- **文件**: `PlusMonthRenewalPlanStateEnum.java`
- **功能**: 定义续费计划状态枚举
- **状态**:
  - GENERATED(1, "已生成")
  - PENDING(2, "待生成")
  - GENERATING(3, "生成中")
  - FAILED(4, "生成失败")
  - CANCELLED(5, "作废")

## 6. 核心业务逻辑

### 6.1 期数设置规则
1. **首单**: 会员月卡类型的首单期数设置为 1
2. **续费订单**: 从续费计划的 `currentPeriod` 字段获取期数
3. **非会员月卡**: 不设置期数（保持 NULL）

### 6.2 续费订单创建流程
1. 定时任务查询当天待生成的续费计划
2. 构建订单创建事件，设置期数为续费计划的 `currentPeriod`
3. 调用订单创建服务
4. 更新续费计划状态和实际生成时间

### 6.3 特殊处理
- 续费订单创建时不触发风控提额通知
- 支付方式设置为后付款（PAY_AFTER）
- 包含完整的异常处理和状态更新机制

## 7. 数据一致性保证

### 7.1 向后兼容
- 新字段允许为 NULL，历史数据不受影响
- 只有会员月卡类型才设置期数

### 7.2 事务保证
- 续费订单创建使用 `@Transactional` 保证数据一致性
- 状态更新和订单创建在同一事务中

## 8. 日志记录
- 期数设置过程有详细日志记录
- 续费订单创建过程有完整的日志跟踪
- 异常情况有错误日志记录

## 9. 测试建议
1. 验证数据库字段添加成功
2. 测试会员月卡首单期数设置为 1
3. 测试续费订单期数从续费计划正确获取
4. 测试定时任务正常执行
5. 测试异常情况的处理和状态更新

## 10. 部署注意事项
1. 先执行数据库变更 SQL
2. 确保定时任务配置正确
3. 验证新字段在各个环境的映射正常
4. 监控续费订单创建的执行情况
