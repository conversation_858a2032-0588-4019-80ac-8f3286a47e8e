package com.juzifenqi.plus.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员月卡续费计划状态枚举
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
@Getter
@AllArgsConstructor
public enum PlusMonthRenewalPlanStateEnum {

    /**
     * 已生成
     */
    GENERATED(1, "已生成"),

    /**
     * 待生成
     */
    PENDING(2, "待生成"),

    /**
     * 生成中
     */
    GENERATING(3, "生成中"),

    /**
     * 生成失败
     */
    FAILED(4, "生成失败"),

    /**
     * 作废
     */
    CANCELLED(5, "作废");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static PlusMonthRenewalPlanStateEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PlusMonthRenewalPlanStateEnum stateEnum : values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }
        return null;
    }
}
