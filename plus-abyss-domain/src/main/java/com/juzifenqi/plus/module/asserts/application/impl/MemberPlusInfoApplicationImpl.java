package com.juzifenqi.plus.module.asserts.application.impl;

import com.juzifenqi.plus.enums.OrderNoticeTypeEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusInfoApplication;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.application.converter.IPlusMemberProfitsCardApplicationConverter;
import com.juzifenqi.plus.module.asserts.model.MemberExpireModel;
import com.juzifenqi.plus.module.asserts.model.PlusMemberCardModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusMemberCardEntity;
import com.juzifenqi.plus.module.asserts.model.event.MemberPlusSendPlanCreateEvent;
import com.juzifenqi.plus.module.asserts.model.event.PlusMemberCardCancelEvent;
import com.juzifenqi.plus.module.asserts.model.event.PlusMemberCardOpenEvent;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.order.application.IPlusOrderNoticeApplication;
import com.juzifenqi.plus.module.order.model.event.notice.PlusOrderNoticeEvent;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberPlusInfoApplicationImpl implements IMemberPlusInfoApplication {

    private final IPlusMemberProfitsCardApplicationConverter converter = IPlusMemberProfitsCardApplicationConverter.instance;

    @Autowired
    private PlusMemberCardModel            plusMemberCardModel;
    @Autowired
    private MemberExpireModel              expireModel;
    @Autowired
    private IMemberPlusSendPlanApplication profitsSendPlanApplication;
    @Autowired
    private IPlusProgramQueryModel         programQueryModel;
    @Autowired
    private IPlusOrderNoticeApplication    noticeApplication;
    /**
     * 开通
     */
    @Override
    public void openCard(PlusMemberCardOpenEvent cardOpenEvent) {
        // 1.基础参数校验
        if (cardOpenEvent.getUserId() == null || cardOpenEvent.getChannelId() == null
                || cardOpenEvent.getProgramEntity() == null || StringUtils.isEmpty(
                cardOpenEvent.getPlusOrderSn())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        // 2. 开通会员卡
        PlusMemberCardEntity plusMemberCard = plusMemberCardModel.openCard(cardOpenEvent);
        // 获取权益包信息
        PlusProgramEntity program = programQueryModel.getProgramAndProfitsPackage(
                cardOpenEvent.getProgramEntity().getProgramId(), cardOpenEvent.getPlusOrderSn());
        // 3. 生成发放计划和发放权益
        MemberPlusSendPlanCreateEvent createEvent = converter.toProfitsSendPlanCreateEvent(
                plusMemberCard, program);
        profitsSendPlanApplication.generateMemberProfitsSendPlan(createEvent);
        // 4. 保存开卡回调任务
        PlusOrderNoticeEvent noticeEvent = converter.toPlusOrderNoticeEvent(plusMemberCard,
                OrderNoticeTypeEnum.OPEN_CARD.getCode());
        noticeApplication.saveNoticeTask(noticeEvent);
    }

    /**
     * 取消会员
     */
    @Override
    public List<MemberPlusInfoDetailExtPo> cancelCard(PlusMemberCardCancelEvent cardCancelEvent) {
        // 基础参数校验
        if (StringUtils.isEmpty(cardCancelEvent.getPlusOrderSn())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        // 取消会员
        return plusMemberCardModel.cancelCard(cardCancelEvent);
    }

    @Override
    public void memberExpire() {
        expireModel.memberExpire();
    }

    @Override
    public void delMemberCacheInfo(Integer channelId, Integer userId, Integer configId) {
        plusMemberCardModel.delMemberCacheInfo(channelId, userId, configId);
    }

    @Override
    public void singlePlusExpire() {
        expireModel.singlePlusExpire();
    }
}
