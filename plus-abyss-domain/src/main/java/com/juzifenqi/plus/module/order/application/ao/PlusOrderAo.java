package com.juzifenqi.plus.module.order.application.ao;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusOrderAo {

    /**
     * 是否开通会员成功
     */
    private boolean openCard;

    /**
     * 降息卡使用，引流标识 1：引流中原
     */
    private Integer leadCode;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * sign,订单中心创单接口返回
     */
    private String sign;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 已生成发放计划的权益id
     */
    private List<Integer> sendPlanModelIds;

    /**
     * 会员月卡第几期（1表示第1期，2表示第2期，以此类推）
     */
    private Integer monthPeriod;
}
