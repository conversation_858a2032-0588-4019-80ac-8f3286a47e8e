package com.juzifenqi.plus.module.order.schedule;

import com.juzifenqi.plus.module.order.application.IPlusMonthMemberRenewalApplication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 会员月卡续费定时任务
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
@Component
@Slf4j
public class PlusMonthMemberRenewalSchedule {

    @Autowired
    private IPlusMonthMemberRenewalApplication renewalApplication;

    /**
     * 定时创建会员月卡续费订单
     * 每天凌晨0点执行
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void createRenewalOrders() {
        log.info("开始执行会员月卡续费订单定时任务");
        
        try {
            renewalApplication.createRenewalOrdersBySchedule();
        } catch (Exception e) {
            log.error("会员月卡续费订单定时任务执行异常", e);
        }
        
        log.info("会员月卡续费订单定时任务执行完成");
    }
}
